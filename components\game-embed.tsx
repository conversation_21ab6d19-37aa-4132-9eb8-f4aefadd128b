"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Play, Maximize2 } from "lucide-react"

export function GameEmbed() {
  const [isGameLoaded, setIsGameLoaded] = useState(false)

  const loadGame = () => {
    setIsGameLoaded(true)
  }

  return (
    <section className="py-16 bg-muted/50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="font-serif text-3xl sm:text-4xl font-bold text-foreground mb-4">Play Chiikawa Puzzle Game</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Experience the fun directly in your browser! No downloads required - just click and play.
          </p>
        </div>

        <Card className="max-w-4xl mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="font-serif text-2xl">Chiikawa Puzzle Adventure</CardTitle>
            <CardDescription>
              Solve puzzles featuring your favorite Chiikawa characters in this engaging brain teaser game.
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            {!isGameLoaded ? (
              <div className="aspect-video bg-gradient-to-br from-primary/10 to-accent/10 rounded-lg flex flex-col items-center justify-center space-y-4">
                <div className="w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center">
                  <Play className="w-12 h-12 text-primary" />
                </div>
                <div className="text-center space-y-2">
                  <h3 className="font-serif text-xl font-semibold">Ready to Play?</h3>
                  <p className="text-muted-foreground">Click the button below to start your puzzle adventure!</p>
                </div>
                <Button onClick={loadGame} size="lg" className="px-8">
                  <Play className="mr-2 h-5 w-5" />
                  Start Game
                </Button>
              </div>
            ) : (
              <div className="relative">
                <iframe
                  src="https://html-classic.itch.zone/html/12096265/index.html"
                  className="w-full aspect-video rounded-lg border-2 border-border"
                  title="Chiikawa Puzzle Game"
                  allowFullScreen
                  loading="lazy"
                />
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={() => {
                    const iframe = document.querySelector("iframe")
                    if (iframe?.requestFullscreen) {
                      iframe.requestFullscreen()
                    }
                  }}
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </section>
  )
}
